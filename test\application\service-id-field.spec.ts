/**
 * Test suite for verifying service_id field inclusion in GET /applications endpoint
 *
 * This test ensures that the service_id field is properly resolved and included
 * in the API response after the recent fixes to the resolveServiceName method
 * and transformer service.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ApplicationService } from '../../src/application/application.service';
import { ApplicationTransformerService } from '../../src/application/services/application-transformer.service';
import { PrismaService } from '../../src/prisma/prisma.service';

describe('Service ID Field in Applications Response', () => {
  let applicationService: ApplicationService;
  let transformerService: ApplicationTransformerService;
  let prismaService: PrismaService;

  const mockPrismaService = {
    application: {
      findMany: jest.fn(),
      count: jest.fn(),
    },
    agent: {
      findMany: jest.fn(),
    },
    immigration_service: {
      findUnique: jest.fn(),
    },
    service: {
      findUnique: jest.fn(),
    },
    packages: {
      findUnique: jest.fn(),
    },
    training: {
      findUnique: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ApplicationService,
        ApplicationTransformerService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    applicationService = module.get<ApplicationService>(ApplicationService);
    transformerService = module.get<ApplicationTransformerService>(
      ApplicationTransformerService,
    );
    prismaService = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('resolveServiceName method', () => {
    it('should return object with serviceName and serviceId for valid service', async () => {
      // Arrange
      const serviceType = 'immigration';
      const serviceId = 'service_123';
      const mockService = { name: 'Work Permit Application' };

      mockPrismaService.immigration_service.findUnique.mockResolvedValue(
        mockService,
      );

      // Act
      const result = await applicationService['resolveServiceName'](
        serviceType,
        serviceId,
      );

      // Assert
      expect(result).toEqual({
        serviceName: 'Work Permit Application',
        serviceId: 'service_123',
      });
      expect(
        mockPrismaService.immigration_service.findUnique,
      ).toHaveBeenCalledWith({
        where: { id: serviceId },
        select: { name: true },
      });
    });

    it('should return fallback string when service not found', async () => {
      // Arrange
      const serviceType = 'immigration';
      const serviceId = 'nonexistent_service';

      mockPrismaService.immigration_service.findUnique.mockResolvedValue(null);

      // Act
      const result = await applicationService['resolveServiceName'](
        serviceType,
        serviceId,
      );

      // Assert
      expect(result).toBe('immigration service');
    });

    it('should return fallback string when serviceId is not provided', async () => {
      // Arrange
      const serviceType = 'immigration';

      // Act
      const result =
        await applicationService['resolveServiceName'](serviceType);

      // Assert
      expect(result).toBe('immigration service');
    });
  });

  describe('enhanceApplicationsWithDetails method', () => {
    it('should include resolved service_id in enhanced applications', async () => {
      // Arrange
      const mockApplications = [
        {
          id: 'app_123',
          service_type: 'immigration',
          service_id: 'service_456',
          agent_ids: ['agent_1'],
        },
      ];

      const mockAgents = [
        { id: 'agent_1', name: 'Agent Smith', email: '<EMAIL>' },
      ];

      const mockService = { name: 'Work Permit Application' };

      mockPrismaService.agent.findMany.mockResolvedValue(mockAgents);
      mockPrismaService.immigration_service.findUnique.mockResolvedValue(
        mockService,
      );

      // Act
      const result =
        await applicationService['enhanceApplicationsWithDetails'](
          mockApplications,
        );

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        id: 'app_123',
        service_type: 'immigration',
        service_id: 'service_456', // Should be the resolved service ID
        service_name: 'Work Permit Application',
        agent_details: [
          { id: 'agent_1', name: 'Agent Smith', email: '<EMAIL>' },
        ],
      });
    });

    it('should handle fallback service_id when service resolution fails', async () => {
      // Arrange
      const mockApplications = [
        {
          id: 'app_123',
          service_type: 'immigration',
          service_id: 'nonexistent_service',
          agent_ids: [],
        },
      ];

      mockPrismaService.agent.findMany.mockResolvedValue([]);
      mockPrismaService.immigration_service.findUnique.mockResolvedValue(null);

      // Act
      const result =
        await applicationService['enhanceApplicationsWithDetails'](
          mockApplications,
        );

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        id: 'app_123',
        service_type: 'immigration',
        service_id: 'nonexistent_service', // Should fallback to original service_id
        service_name: 'immigration service',
        agent_details: [],
      });
    });
  });

  describe('transformApplicationListItem method', () => {
    it('should include service_id field in transformed response', () => {
      // Arrange
      const mockApplication = {
        id: 'app_123',
        application_number: 'IMM-2024-000001',
        service_type: 'immigration',
        service_id: 'service_456',
        service_name: 'Work Permit Application',
        status: 'draft',
        priority_level: 'medium',
        current_step: '1',
        agent_details: [],
        estimated_completion: null,
        created_at: new Date('2024-01-01'),
        updated_at: new Date('2024-01-01'),
        workflow_template: {
          workflowTemplate: [{ stageOrder: 1 }, { stageOrder: 2 }],
        },
      };

      // Act
      const result =
        transformerService.transformApplicationListItem(mockApplication);

      // Assert
      expect(result).toMatchObject({
        id: 'app_123',
        application_number: 'IMM-2024-000001',
        service_type: 'immigration',
        service_id: 'service_456', // This field should be present
        service_name: 'Work Permit Application',
        status: 'draft',
        priority_level: 'medium',
        current_step: '1',
        numberOfSteps: 2,
        agent_ids: [],
      });
    });
  });

  describe('transformApplicationDetails method', () => {
    it('should include service_id field in detailed response', () => {
      // Arrange
      const mockApplication = {
        id: 'app_123',
        application_number: 'IMM-2024-000001',
        service_type: 'immigration',
        service_id: 'service_456',
        service_name: 'Work Permit Application',
        status: 'draft',
        note: 'Test note',
        priority_level: 'medium',
        current_step: '1',
        estimated_completion: null,
        agent_ids: [],
        created_at: new Date('2024-01-01'),
        updated_at: new Date('2024-01-01'),
        workflow_template: {
          id: 'template_123',
          name: 'Immigration Template',
          description: 'Template for immigration applications',
          workflowTemplate: [{ stageOrder: 1 }, { stageOrder: 2 }],
        },
      };

      // Act
      const result =
        transformerService.transformApplicationDetails(mockApplication);

      // Assert
      expect(result).toMatchObject({
        id: 'app_123',
        application_number: 'IMM-2024-000001',
        service_type: 'immigration',
        service_id: 'service_456', // This field should be present
        service_name: 'Work Permit Application',
        status: 'draft',
        note: 'Test note',
        priority_level: 'medium',
        current_step: '1',
        numberOfSteps: 2,
      });
    });
  });
});
