/**
 * Core interfaces for the Dynamic Workflow System
 * Task 2: Core Service Abstractions Implementation
 */

import {
  ApplicationStatus,
  WorkflowStepStatus,
  DocumentStatus,
  PriorityLevel,
} from '@prisma/client';

/**
 * Core Application interface matching the database schema
 */
export interface IApplication {
  id: string;
  application_number: string;
  // REMOVED: application_type field - following non-destructive patterns
  // application_type: string;
  service_type: string;
  service_id?: string;
  user_id?: string;
  guest_name?: string;
  guest_email?: string;
  guest_mobile?: string;
  status: ApplicationStatus;
  // REMOVED: current_step_id field - replaced with current_step integer
  // current_step_id?: string;
  current_step: number; // Current workflow step number
  workflow_template_id?: string;
  // REMOVED: metadata field - following non-destructive patterns
  // metadata?: any;
  priority_level: PriorityLevel;
  // REMOVED: sla_deadline field - following non-destructive patterns
  // sla_deadline?: Date;
  estimated_completion?: Date;
  note?: string; // Application notes field for internal use
  created_at: Date;
  updated_at: Date;
  created_by?: string;
  agent_ids?: string[];

  // Optional relationships (when included in queries)
  user?: {
    id: string;
    name: string;
    email: string;
  };
  payment?: any;
  workflow_template?: IWorkflowTemplate;
  // REMOVED: current_step relationship - replaced with current_step integer field
  // current_step?: IApplicationStep;
  steps?: IApplicationStep[];
  documents?: Array<{
    document: IDocumentVault;
  }>;
  notifications?: any[];
}

/**
 * Workflow Template interface - Updated to match current database schema
 */
export interface IWorkflowTemplate {
  id: string;
  name: string;
  description?: string;
  serviceType: string;
  serviceId?: string;
  isActive: boolean;
  workflowTemplate: any;
  createdBy?: string;
  updatedBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Application Step interface
 */
export interface IApplicationStep {
  id: string;
  application_id: string;
  step_name: string;
  step_order: number;
  status: WorkflowStepStatus;
  required_fields?: any;
  validation_rules?: any;
  completion_criteria?: any;
  estimated_duration?: number;
  sla_threshold?: number;
  started_at?: Date;
  completed_at?: Date;
  due_date?: Date;
  assignee_role?: string;
  assigned_to?: string;
  reviewer_id?: string;
  review_notes?: string;
  step_data?: any;
  attachments?: any;
  created_at: Date;
  updated_at: Date;
}

/**
 * Document Vault interface
 */
export interface IDocumentVault {
  id: string;
  document_name: string;
  original_filename: string;
  document_type: string;
  document_category?: string;
  file_path: string;
  file_size: number;
  expiry_date?: Date;
  expiry_reminder_sent: boolean;
  auto_renewal_enabled: boolean;
  user_id?: string;
  guest_email?: string;
  uploaded_by?: string;
  uploaded_at: Date;
  created_at: Date;
  updated_at: Date;
}

/**
 * Notification interface
 */
export interface INotification {
  id: string;
  notification_type: string;
  template_id?: string;
  recipient_user_id?: string;
  recipient_email: string;
  recipient_mobile?: string;
  subject?: string;
  message_body: string;
  status: string;
  scheduled_at?: Date;
  sent_at?: Date;
  delivery_attempts: number;
  last_attempt_at?: Date;
  error_message?: string;
  application_id?: string;
  document_id?: string;
  metadata?: any;
  created_at: Date;
  updated_at: Date;
}

/**
 * Service data interface for polymorphic service references
 */
export interface IServiceData {
  id: string;
  name: string;
  description?: string;
  amount: number;
  service?: string[];
  order?: number;
  mentor?: string;
  meeting_link?: string;
  [key: string]: any;
}

/**
 * Application creation context interface
 */
export interface IApplicationCreationContext {
  payment: any;
  workflowTemplate: IWorkflowTemplate;
  userData?: {
    user_id?: string;
    guest_name?: string;
    guest_email?: string;
    guest_mobile?: string;
  };
  serviceData: IServiceData;
  metadata?: any;
}

/**
 * Step progression context interface
 */
export interface IStepProgressionContext {
  applicationId: string;
  stepNumber: number;
  stepData?: any;
  assignedTo?: string;
  reviewNotes?: string;
  attachments?: any;
}

/**
 * Workflow engine configuration interface
 */
export interface IWorkflowEngineConfig {
  enableSlaTracking: boolean;
  enableNotifications: boolean;
  defaultPriority: PriorityLevel;
  maxRetryAttempts: number;
}

/**
 * Abstract application service interface
 */
export interface IAbstractApplicationService<T = any> {
  createApplicationFromPayment(
    context: IApplicationCreationContext,
  ): Promise<IApplication>;
  updateStepStatus(context: IStepProgressionContext): Promise<IApplicationStep>;
  getApplicationDetails(applicationId: string): Promise<IApplication | null>;
  generateApplicationNumber(serviceType: string): Promise<string>;
  transformApplicationDetails(application: IApplication): Promise<T>;
  getServiceSpecificData(serviceId: string): Promise<IServiceData>;
  validateApplicationRequirements(application: IApplication): Promise<boolean>;
}

/**
 * Workflow engine service interface
 */
export interface IWorkflowEngineService {
  initializeWorkflow(
    application: IApplication,
    template: IWorkflowTemplate,
  ): Promise<IApplicationStep[]>;
  advanceWorkflow(
    applicationId: string,
    stepNumber: number,
    data?: any,
  ): Promise<IApplicationStep>;
  validateStepCompletion(step: IApplicationStep, data?: any): Promise<boolean>;
  checkStepDependencies(
    step: IApplicationStep,
    allSteps: IApplicationStep[],
  ): Promise<boolean>;
  checkOverdueSteps(): Promise<IApplicationStep[]>;
  getActiveTemplate(
    applicationType: string,
    serviceType?: string,
  ): Promise<IWorkflowTemplate | null>;
}

/**
 * Notification service interface
 */
export interface INotificationService {
  sendNotification(
    notification: Partial<INotification>,
  ): Promise<INotification>;
  scheduleNotification(
    notification: Partial<INotification>,
    scheduledAt: Date,
  ): Promise<INotification>;
  processNotificationQueue(): Promise<void>;
  createNotificationFromTemplate(
    templateId: string,
    context: any,
  ): Promise<INotification>;
}

/**
 * Document vault service interface
 */
export interface IDocumentVaultService {
  uploadDocument(
    file: any,
    metadata: any,
    userId?: string,
  ): Promise<IDocumentVault>;
  getUserVault(
    userId: string,
    filters?: any,
  ): Promise<{
    documents: IDocumentVault[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }>;
  getUserVaultLegacy(userId: string, filters?: any): Promise<IDocumentVault[]>;
  checkExpiringDocuments(): Promise<IDocumentVault[]>;
  linkDocumentToApplication(
    documentId: string,
    applicationId: string,
  ): Promise<void>;
}
